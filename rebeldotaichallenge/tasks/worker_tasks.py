import logging
import time
from typing import Any, Dict, List, Literal

from langchain_core.documents import Document
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate

from rebeldotaichallenge._params import DATABASE_URL
from rebeldotaichallenge.database.pg_vector import LangChainPGVectorStore
from rebeldotaichallenge.llm.llm_providers.openai_call import classifier_llm
from rebeldotaichallenge.tasks.celery_app import celery_app
from rebeldotaichallenge.utils.prompts import CLASSIFY_QUESTION_PROMPT

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="process_question_task")
def process_question_task(
    self, question: str, collection_name: str = "default", **kwargs
) -> Dict[str, Any]:
    """
    Process a question using vector similarity search.

    Args:
        question: The question to process
        collection_name: Name of the vector collection to search
        **kwargs: Additional parameters

    Returns:
        Dictionary containing the results
    """
    print(question)
    print(collection_name)
    print(kwargs)
    return {}


@celery_app.task(bind=True, name="health_check_task")
def health_check_task(self) -> Dict[str, Any]:
    """
    Simple health check task to verify Celery is working.

    Returns:
        Dictionary containing health check results
    """
    try:
        task_id = self.request.id
        logger.info(f"Starting health check task {task_id}")

        # Simulate some work
        time.sleep(1)

        result = {
            "task_id": task_id,
            "status": "healthy",
            "message": "Celery worker is functioning correctly",
            "timestamp": time.time(),
        }

        logger.info(f"Health check task {task_id} completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in health check task {self.request.id}: {str(e)}")
        self.update_state(state="FAILURE", meta={"error": str(e), "status": "failed"})
        raise


# New router task for question classification
@celery_app.task(bind=True, name="classify_question_task")
def classify_question_task(self, question: str) -> Literal["IT", "non-IT"]:
    """
    Classify if question is IT-related using LangChain router
    """
    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", CLASSIFY_QUESTION_PROMPT),
            ("human", "Classify the following question: {question}"),
        ]
    )

    router = prompt | classifier_llm
    result = router.invoke({"question": question})

    return result.classification


@celery_app.task(bind=True, name="compliance_response_task")
def compliance_response_task(self, question: str) -> Dict[str, Any]:
    """
    Fast compliance response for non-IT questions
    """
    return {
        "matched_question": "N/A",
        "answer": "This is not really what I was trained for, therefore I cannot answer. Try again.",
    }
